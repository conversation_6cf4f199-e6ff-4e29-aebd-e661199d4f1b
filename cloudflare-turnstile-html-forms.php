<?php
/**
 * Plugin Name:       Cloudflare Turnstile for HTML Forms
 * Description:       Integrates Cloudflare Turnstile with HTML Forms plugin.
 * Version:           1.0.0
 * Author:            <PERSON>
 * Author URI:        https://www.janpencik.cz/
 * Text Domain:       cf-turnstile-html-forms
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

/**
 * Main plugin class.
 */
class WP_Cloudflare_Turnstile_Custom_Form {

    /**
     * Constructor.
     */
    public function __construct() {
        add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );
        add_action( 'admin_init', array( $this, 'register_settings' ) );
        add_shortcode( 'cf_turnstile', array( $this, 'turnstile_shortcode' ) );

        // === HTML Forms Plugin Integration ===
        // Hook into HTML Forms validation process
        add_filter( 'hf_validate_form', array( $this, 'validate_turnstile_on_html_form' ), 10, 3 );
        // Register custom error message for Turnstile failure
        add_filter( 'hf_form_message_cf_turnstile_failed', array( $this, 'turnstile_error_message' ) );
    }

    /**
     * Adds the admin menu page.
     */
    public function add_admin_menu() {
        add_options_page(
            __( 'Cloudflare Turnstile', 'wp-cf-turnstile' ),
            __( 'Cloudflare Turnstile', 'wp-cf-turnstile' ),
            'manage_options',
            'cloudflare-turnstile-settings',
            array( $this, 'settings_page_html' )
        );
    }

    /**
     * Registers plugin settings.
     */
    public function register_settings() {
        register_setting(
            'cloudflare_turnstile_options', // Option group
            'cloudflare_turnstile_site_key',  // Option name for Site Key
            array(
                'type'              => 'string',
                'sanitize_callback' => 'sanitize_text_field',
                'default'           => '',
            )
        );

        register_setting(
            'cloudflare_turnstile_options', // Option group
            'cloudflare_turnstile_secret_key', // Option name for Secret Key
            array(
                'type'              => 'string',
                'sanitize_callback' => 'sanitize_text_field',
                'default'           => '',
            )
        );

        add_settings_section(
            'cloudflare_turnstile_main_section', // ID
            __( 'Cloudflare Turnstile API Keys', 'wp-cf-turnstile' ), // Title
            array( $this, 'settings_section_callback' ), // Callback
            'cloudflare-turnstile-settings' // Page
        );

        add_settings_field(
            'cloudflare_turnstile_site_key_field', // ID
            __( 'Site Key', 'wp-cf-turnstile' ), // Title
            array( $this, 'site_key_callback' ), // Callback
            'cloudflare-turnstile-settings', // Page
            'cloudflare_turnstile_main_section' // Section
        );

        add_settings_field(
            'cloudflare_turnstile_secret_key_field', // ID
            __( 'Secret Key', 'wp-cf-turnstile' ), // Title
            array( $this, 'secret_key_callback' ), // Callback
            'cloudflare-turnstile-settings', // Page
            'cloudflare_turnstile_main_section' // Section
        );
    }

    /**
     * Settings section callback.
     */
    public function settings_section_callback() {
        echo '<p>' . esc_html__( 'Enter your Cloudflare Turnstile Site Key and Secret Key below. You can find these in your Cloudflare dashboard under the "Turnstile" section.', 'wp-cf-turnstile' ) . '</p>';
    }

    /**
     * Renders the Site Key input field.
     */
    public function site_key_callback() {
        $site_key = get_option( 'cloudflare_turnstile_site_key', '' );
        echo '<input type="text" id="cloudflare_turnstile_site_key" name="cloudflare_turnstile_site_key" value="' . esc_attr( $site_key ) . '" class="regular-text" placeholder="e.g., 0x4AAAAAAAPo-V1S9xQW-C...">';
    }

    /**
     * Renders the Secret Key input field.
     */
    public function secret_key_callback() {
        $secret_key = get_option( 'cloudflare_turnstile_secret_key', '' );
        echo '<input type="text" id="cloudflare_turnstile_secret_key" name="cloudflare_turnstile_secret_key" value="' . esc_attr( $secret_key ) . '" class="regular-text" placeholder="e.g., 0x4AAAAAAAPxM-R1UaW-W_y_a_R...">';
    }

    /**
     * Renders the settings page HTML.
     */
    public function settings_page_html() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>
            <form action="options.php" method="post">
                <?php
                settings_fields( 'cloudflare_turnstile_options' );
                do_settings_sections( 'cloudflare-turnstile-settings' );
                submit_button();
                ?>
            </form>
        </div>
        <?php
    }

    /**
     * Shortcode to display the Cloudflare Turnstile widget.
     * [cf_turnstile]
     *
     * @return string HTML for the Turnstile widget.
     */
    public function turnstile_shortcode() {
        $site_key = get_option( 'cloudflare_turnstile_site_key' );

        if ( empty( $site_key ) ) {
            // Only show error if site key is not set, otherwise it will just be an empty div for visitors.
            if ( current_user_can( 'manage_options' ) ) {
                return '<p style="color:red;">' . esc_html__( 'Cloudflare Turnstile: Site Key is not configured. Please set it in Settings > Cloudflare Turnstile.', 'wp-cf-turnstile' ) . '</p>';
            }
            return ''; // Don't output anything for regular users if misconfigured.
        }

        // Enqueue the Turnstile script.
        wp_enqueue_script(
            'cloudflare-turnstile',
            'https://challenges.cloudflare.com/turnstile/v0/api.js',
            array(),
            null, // No version needed, it's dynamic
            true  // Load in footer
        );

        // Generate a unique ID for the Turnstile div to prevent conflicts if multiple forms on page
        // Use this raw ID for HTML attributes (which can contain hyphens)
        $turnstile_id_raw = 'cf-turnstile-' . uniqid();
        // Convert the raw ID to a JS-safe format (replace hyphens with underscores) for function names
        $turnstile_id_js  = str_replace( '-', '_', $turnstile_id_raw );

        ob_start();
        ?>
        <div class="cf-turnstile"
             data-sitekey="<?php echo esc_attr( $site_key ); ?>"
             data-callback="cfTurnstileCallback_<?php echo esc_attr( $turnstile_id_js ); ?>"
             data-error-callback="cfTurnstileErrorCallback_<?php echo esc_attr( $turnstile_id_js ); ?>"
             data-theme="light"></div>
        <input type="hidden" name="cf-turnstile-response" id="cf-turnstile-response-<?php echo esc_attr( $turnstile_id_raw ); ?>" value="">

        <script type="text/javascript">
            // This function will be called by Turnstile when a challenge is successfully completed.
            // It puts the token into the hidden input field.
            function cfTurnstileCallback_<?php echo esc_js( $turnstile_id_js ); ?>(token) {
                // The ID of the hidden input uses the raw ID for consistency with HTML
                document.getElementById('cf-turnstile-response-<?php echo esc_js( $turnstile_id_raw ); ?>').value = token;
            }

            // Optional: Error callback (called if Turnstile encounters a client-side error)
            function cfTurnstileErrorCallback_<?php echo esc_js( $turnstile_id_js ); ?>() {
                // You can add custom error handling here, e.g., display an error message
                // console.error('Cloudflare Turnstile challenge failed client-side for ID: <?php echo esc_js( $turnstile_id_raw ); ?>');
                document.getElementById('cf-turnstile-response-<?php echo esc_js( $turnstile_id_raw ); ?>').value = ''; // Clear token on error
            }
        </script>
        <?php
        return ob_get_clean();
    }

    /**
     * Helper function to verify the Turnstile token server-side.
     * Call this from your custom form submission handler.
     *
     * @param string $token The cf-turnstile-response token from $_POST.
     * @return bool True if verification is successful, false otherwise.
     */
    public static function verify_turnstile( $token ) {
        $secret_key = get_option( 'cloudflare_turnstile_secret_key' );

        if ( empty( $secret_key ) ) {
            error_log( 'Cloudflare Turnstile Verification Error: Secret Key is not configured.' );
            return false;
        }

        if ( empty( $token ) ) {
            error_log( 'Cloudflare Turnstile Verification Error: No token provided.' );
            return false;
        }

        $response = wp_remote_post( 'https://challenges.cloudflare.com/turnstile/v0/siteverify', array(
            'body' => array(
                'secret'   => $secret_key,
                'response' => $token,
                'remoteip' => $_SERVER['REMOTE_ADDR'] ?? '', // Optional: Pass user's IP for better scoring
            ),
            'timeout' => 10, // Increased timeout for external API call
        ) );

        if ( is_wp_error( $response ) ) {
            error_log( 'Cloudflare Turnstile Verification Error: ' . $response->get_error_message() );
            return false;
        }

        $body = wp_remote_retrieve_body( $response );
        $data = json_decode( $body, true );

        if ( ! isset( $data['success'] ) || ! $data['success'] ) {
            error_log( 'Cloudflare Turnstile Verification Failed. Errors: ' . json_encode( $data['error-codes'] ?? 'None' ) );
            return false;
        }

        return true;
    }

    /**
     * HTML Forms Integration: Validates the Turnstile response.
     * This function is hooked into the `hf_validate_form` filter.
     *
     * @param string $error_code The current error code (empty if no error yet).
     * @param object $form       The HTML Forms form object.
     * @param array  $data       The submitted form data.
     * @return string            The error code, or empty string if validation passes.
     */
    public function validate_turnstile_on_html_form( $error_code, $form, $data ) {
        // If an error already exists, don't proceed with Turnstile validation to avoid overwriting.
        if ( ! empty( $error_code ) ) {
            return $error_code;
        }

        // Check if the form includes the cf-turnstile-response field (meaning our shortcode was used).
        if ( isset( $data['cf-turnstile-response'] ) ) {
            $turnstile_response_token = sanitize_text_field( $data['cf-turnstile-response'] );

            // Perform server-side validation.
            if ( ! self::verify_turnstile( $turnstile_response_token ) ) {
                // If verification fails, set a custom error code.
                $error_code = 'cf_turnstile_failed';
            }
        }
        // If the field is not present, it means this form doesn't use Turnstile, so we don't interfere.

        return $error_code;
    }

    /**
     * HTML Forms Integration: Provides the error message for Turnstile failure.
     * This function is hooked into the `hf_form_message_cf_turnstile_failed` filter.
     *
     * @param string $message The default message (usually empty).
     * @return string The custom error message.
     */
    public function turnstile_error_message( $message ) {
        return __( 'CAPTCHA verification failed. Please check the CAPTCHA and try again.', 'wp-cf-turnstile' );
    }
}

// Initialize the plugin.
new WP_Cloudflare_Turnstile_Custom_Form();