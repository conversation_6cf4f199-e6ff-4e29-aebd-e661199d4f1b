<?php
// Benefits section for flexible content block
$anchor    = get_sub_field('anchor');
$heading   = get_sub_field('heading');
$text      = get_sub_field('text');
$benefits  = get_sub_field('benefits');
$accordion = get_sub_field('accordion');
$button_1  = get_sub_field('button_1');
$button_2  = get_sub_field('button_2');
?>
<section class="benefits" <?php if ($anchor) : ?>id="<?php echo esc_attr($anchor); ?>"<?php endif; ?>>
    <div class="container">
        <div class="row">
            <?php if ($heading) : ?>
                <h2><?php echo esc_html($heading); ?></h2>
            <?php endif; ?>
        </div>
        <?php if ($benefits) : ?>
            <div class="row flex flex-wrap benefits__items">
                <?php foreach ($benefits as $benefit) : ?>
                    <div class="col">
                        <?php if ($benefit['icon']) : ?>
                            <?php echo $benefit['icon']; ?>
                        <?php endif; ?>
                        <?php if ($benefit['name']) : ?>
                            <h3><?php echo esc_html($benefit['name']); ?></h3>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        <?php if ($text) : ?>
            <div class="row benefits__text">
                <?php echo wp_kses_post($text); ?>
            </div>
        <?php endif; ?>
        <?php if ($accordion) : ?>
            <div class="row accordion benefits__accordion">
                <?php
                $i = 0;
                foreach ($accordion as $item) :
                    $active = $i === 0 ? 'active' : '';
                    $accordion_id = $item['title'] ? sanitize_title($item['title']) : '';
                    $i++;
                    ?>
                    <div class="accordion-item <?php echo $active; ?>">
                        <div class="accordion-header" <?php if ($accordion_id) : ?>id="<?php echo esc_attr($accordion_id); ?>"<?php endif; ?>>
                            <?php if ($item['title']) : ?>
                                <h3>
                                    <?php if ($accordion_id) : ?>
                                        <a href="#<?php echo esc_attr($accordion_id); ?>">
                                            <span><?php echo esc_html($item['title']); ?></span>
                                        </a>
                                    <?php else : ?>
                                        <span><?php echo esc_html($item['title']); ?></span>
                                    <?php endif; ?>
                                </h3>
                            <?php endif; ?>
                        </div>
                        <div class="accordion-content">
                            <?php if ($item['text']) : ?>
                                <?php echo wp_kses_post($item['text']); ?>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        <?php if ($button_1 || $button_2) : ?>
            <div class="row flex justify-center flex-wrap row--buttons">
                <?php if ($button_1) : ?>
                    <a class="button button--cta" href="<?php echo esc_url($button_1['url']); ?>" target="<?php echo esc_attr($button_1['target']); ?>">
                        <?php echo esc_html($button_1['title']); ?>
                    </a>
                <?php endif; ?>
                <?php if ($button_2) : ?>
                    <a class="button button--link" href="<?php echo esc_url($button_2['url']); ?>" target="<?php echo esc_attr($button_2['target']); ?>">
                        <?php echo esc_html($button_2['title']); ?>
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</section>
