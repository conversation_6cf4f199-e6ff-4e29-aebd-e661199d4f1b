<?php
// Hero section for flexible content block
$anchor    = get_sub_field('anchor');
$heading   = get_sub_field('heading');
$text      = get_sub_field('text');
$img_left  = get_sub_field('img_left', false, false);
$icon_1    = get_sub_field('icon_1');
$icon_2    = get_sub_field('icon_2');
$icon_3    = get_sub_field('icon_3');
$heading_1 = get_sub_field('heading_1');
$heading_2 = get_sub_field('heading_2');
$heading_3 = get_sub_field('heading_3');
$text_1    = get_sub_field('text_1');
$text_2    = get_sub_field('text_2');
$text_3    = get_sub_field('text_3');
$button_1  = get_sub_field('button_1');
$button_2  = get_sub_field('button_2');
?>
<section class="hero flex" <?php if ($anchor) : ?>id="<?php echo esc_attr($anchor); ?>"<?php endif; ?>>
    <div class="container flex flex-column justify-center">
        <div class="row hero__content">
            <?php if ($heading) : ?>
                <h1><?php echo esc_html($heading); ?></h1>
            <?php endif; ?>
            <?php if ($text) : ?>
                <?php echo wp_kses_post($text); ?>
            <?php endif; ?>
        </div>
        <div class="row flex flex-wrap hero__items">
            <div class="col hero__items__img">
                <?php if ($img_left) : ?>
                    <?php echo wp_kses_post($img_left); ?>
                <?php endif; ?>
            </div>
            <div class="row hero__items__icons">
                <div class="col hero__items__icon">
                    <?php if ($icon_1) : ?>
                        <?php 
                        if ($icon_1['mime_type'] === 'image/svg+xml') :
                            $svg_code = file_get_contents($icon_1['url']);
                            echo $svg_code;
                        else :
                        ?>
                            <img src="<?php echo esc_url($icon_1['url']); ?>" alt="<?php echo esc_attr($icon_1['alt']); ?>" />
                        <?php endif; ?>
                    <?php endif; ?>
                    <h2><?php echo esc_html($heading_1); ?></h2>
                    <?php echo wp_kses_post($text_1); ?>
                </div>
                <div class="col hero__items__icon">
                    <?php if ($icon_2) : ?>
                        <?php 
                        if ($icon_2['mime_type'] === 'image/svg+xml') :
                            $svg_code = file_get_contents($icon_2['url']);
                            echo $svg_code;
                        else :
                        ?>
                            <img src="<?php echo esc_url($icon_2['url']); ?>" alt="<?php echo esc_attr($icon_2['alt']); ?>" />
                        <?php endif; ?>
                    <?php endif; ?>
                    <h2><?php echo esc_html($heading_2); ?></h2>
                    <?php echo wp_kses_post($text_2); ?>
                </div>
                <div class="col hero__items__icon">
                    <?php if ($icon_3) : ?>
                        <?php 
                        if ($icon_3['mime_type'] === 'image/svg+xml') :
                            $svg_code = file_get_contents($icon_3['url']);
                            echo $svg_code;
                        else :
                        ?>
                            <img src="<?php echo esc_url($icon_3['url']); ?>" alt="<?php echo esc_attr($icon_3['alt']); ?>" />
                        <?php endif; ?>
                    <?php endif; ?>
                    <h2><?php echo esc_html($heading_3); ?></h2>
                    <?php echo wp_kses_post($text_3); ?>
                </div>
            </div>
        </div>
        <?php if ($button_1 || $button_2) : ?>
            <div class="row flex justify-center flex-wrap row--buttons">
                <?php if ($button_1) : ?>
                    <a class="button button--cta-white" href="<?php echo esc_url($button_1['url']); ?>" target="<?php echo esc_attr($button_1['target']); ?>">
                        <?php echo esc_html($button_1['title']); ?>
                    </a>
                <?php endif; ?>
                <?php if ($button_2) : ?>
                    <a class="button button--link-white" href="<?php echo esc_url($button_2['url']); ?>" target="<?php echo esc_attr($button_2['target']); ?>">
                        <?php echo esc_html($button_2['title']); ?>
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</section>