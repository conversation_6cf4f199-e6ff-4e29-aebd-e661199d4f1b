// Mobile menu toggle
const menuToggle = document.querySelector('.menu__toggle');
const body = document.querySelector('body');

menuToggle.addEventListener('click', function () {
  body.classList.toggle('mobile-menu-open');
});

// Close mobile menu when clicking on menu links
document.addEventListener('click', function (e) {
  const menuLink = e.target.closest('.header__menu a');
  if (menuLink && body.classList.contains('mobile-menu-open')) {
    // Only close on mobile devices (when menu toggle is visible)
    const menuToggle = document.querySelector('.menu__toggle');
    const isMenuToggleVisible = window.getComputedStyle(menuToggle).display !== 'none';

    if (isMenuToggleVisible) {
      body.classList.remove('mobile-menu-open');
    }
  }
});

// Fix header position for admin bar
if (body.classList.contains('admin-bar')) {
  window.addEventListener('scroll', function () {
    if (window.scrollY > 46) {
      body.classList.add('scrolled-under-admin-bar');
    } else {
      body.classList.remove('scrolled-under-admin-bar');
    }
  });
}

// Accordion functionality
document.addEventListener('DOMContentLoaded', function () {
  const accordionItems = document.querySelectorAll('.accordion-item');
  
  // Function to update accordion content heights
  function updateAccordionHeights() {
    accordionItems.forEach(item => {
      const content = item.querySelector('.accordion-content');
      
      // Store the actual height as a data attribute
      if (!content.style.maxHeight || content.style.maxHeight === '0px') {
        // Temporarily make the content visible but hidden to measure its height
        content.style.visibility = 'hidden';
        content.style.maxHeight = 'none';
        content.dataset.height = content.scrollHeight + 'px';
        
        // Reset to initial state
        content.style.visibility = '';
        content.style.maxHeight = item.classList.contains('active') ? content.dataset.height : '0px';
      } else {
        // Just update the stored height
        content.style.maxHeight = 'none';
        content.dataset.height = content.scrollHeight + 'px';
        content.style.maxHeight = item.classList.contains('active') ? content.dataset.height : '0px';
      }
    });
  }
  
  // Function to toggle accordion item
  function toggleAccordionItem(item, forceOpen = false) {
    const content = item.querySelector('.accordion-content');
    const isCurrentlyActive = item.classList.contains('active');

    if (isCurrentlyActive && !forceOpen) {
      content.style.maxHeight = '0px';
      item.classList.remove('active');
    } else if (!isCurrentlyActive || forceOpen) {
      item.classList.add('active');
      content.style.maxHeight = content.dataset.height;
    }
  }

  // Add click event listeners to all accordion headers
  accordionItems.forEach(item => {
    const header = item.querySelector('.accordion-header');
    const link = header.querySelector('a');

    // Handle clicks on accordion headers (both with and without links)
    header.addEventListener('click', function (e) {
      // If there's a link, let the browser handle the hash navigation
      if (link) {
        // Prevent the default click behavior on the header itself
        // The link will handle the navigation
        return;
      }

      // For headers without links, toggle normally
      e.preventDefault();
      toggleAccordionItem(item);
    });

    // Handle link clicks specifically
    if (link) {
      link.addEventListener('click', function (e) {
        e.preventDefault();

        // Get the target ID from the href
        const targetId = this.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);

        if (targetElement) {
          // Toggle the accordion
          toggleAccordionItem(item, true); // Force open when clicking link

          // Update the URL hash
          history.pushState(null, null, '#' + targetId);

          // Scroll to the element (browser will handle scroll-margin-top)
          targetElement.scrollIntoView({ behavior: 'smooth' });
        }
      });
    }
  });
  
  // Initial height calculation
  updateAccordionHeights();

  // Handle direct links to accordion items (when page loads with hash)
  function handleHashOnLoad() {
    const hash = window.location.hash;
    if (hash) {
      const targetElement = document.querySelector(hash);
      if (targetElement && targetElement.classList.contains('accordion-header')) {
        // Find the parent accordion item
        const accordionItem = targetElement.closest('.accordion-item');
        if (accordionItem) {
          // Open the accordion
          toggleAccordionItem(accordionItem, true);

          // Scroll to the element after a short delay to ensure accordion is open
          setTimeout(() => {
            targetElement.scrollIntoView({ behavior: 'smooth' });
          }, 100);
        }
      }
    }
  }

  // Handle hash on page load
  handleHashOnLoad();

  // Update heights on window resize
  window.addEventListener('resize', updateAccordionHeights);
});

// Use Swiper without ES module imports
const swiper = new Swiper('.swiper-container', {
  loop: true,
  autoplay: {
    delay: 3000,
    disableOnInteraction: false,
  },
  navigation: false,
  allowTouchMove: false,
  effect: 'fade',
  fadeEffect: {
    crossFade: true,
  }
});

// Popup functionality
document.addEventListener('DOMContentLoaded', function () {
  // Handle links with href containing #popup_id
  document.addEventListener('click', function (e) {
    const link = e.target.closest('a[href*="#"]');
    if (link) {
      const href = link.getAttribute('href');
      const hashIndex = href.indexOf('#');
      if (hashIndex !== -1) {
        const popupId = href.substring(hashIndex + 1);

        // Check if this is a Calendly popup link
        if (typeof survilla_vars !== 'undefined' &&
            survilla_vars.calendly_id &&
            survilla_vars.calendly_url &&
            popupId === survilla_vars.calendly_id) {
          e.preventDefault();
          // Use Calendly popup widget instead of custom popup
          if (typeof Calendly !== 'undefined') {
            Calendly.initPopupWidget({url: survilla_vars.calendly_url});
          }
          return;
        }

        // Handle regular popups
        const popup = document.getElementById(popupId);
        const body = document.querySelector('body');
        if (popup && popup.classList.contains('popup')) {
          e.preventDefault();

          // Check for video URL data attribute
          const videoUrl = link.getAttribute('data-video-url');
          if (videoUrl) {
            populateVideoContent(popup, videoUrl);
          }

          popup.classList.add('show');
          body.classList.add('popup-open');
        }
      }
    }
  });

  // Handle close button clicks
  document.addEventListener('click', function (e) {
    if (e.target.closest('.popup__close')) {
      const popup = e.target.closest('.popup');
      const body = document.querySelector('body');
      if (popup) {
        pauseAndClearVideoContent(popup);
        popup.classList.remove('show');
        body.classList.remove('popup-open');
      }
    }
  });

  // Handle clicks outside popup inner content
  document.addEventListener('click', function (e) {
    if (e.target.classList.contains('popup') && e.target.classList.contains('show')) {
      // Clicked directly on the popup overlay (outside popup__inner)
      pauseAndClearVideoContent(e.target);
      e.target.classList.remove('show');
      document.querySelector('body').classList.remove('popup-open');
    }
  });
});

// Video popup functionality
function populateVideoContent(popup, videoUrl) {
  // For video popups, we'll add content directly to the popup
  // Clear existing content first
  const existingVideo = popup.querySelector('.video-iframe, .video-element');
  if (existingVideo) {
    existingVideo.remove();
  }

  let videoElement;

  if (isYouTubeUrl(videoUrl)) {
    videoElement = createYouTubeIframe(videoUrl);
  } else if (isVimeoUrl(videoUrl)) {
    videoElement = createVimeoIframe(videoUrl);
  } else {
    videoElement = createVideoElement(videoUrl);
  }

  if (videoElement) {
    // Add video class to popup for styling
    popup.classList.add('popup--video');
    popup.appendChild(videoElement);
  }
}

function isYouTubeUrl(url) {
  return url.includes('youtube.com') || url.includes('youtu.be');
}

function isVimeoUrl(url) {
  return url.includes('vimeo.com');
}

function createYouTubeIframe(url) {
  const iframe = document.createElement('iframe');
  const videoId = extractYouTubeVideoId(url);

  if (!videoId) return null;

  iframe.src = `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0`;
  iframe.width = '100%';
  iframe.height = '100%';
  iframe.style.border = 'none';
  iframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share';
  iframe.allowFullscreen = true;
  iframe.title = 'YouTube video player';
  iframe.className = 'video-iframe';

  return iframe;
}

function createVimeoIframe(url) {
  const iframe = document.createElement('iframe');
  const videoId = extractVimeoVideoId(url);

  if (!videoId) return null;

  iframe.src = `https://player.vimeo.com/video/${videoId}?autoplay=1`;
  iframe.width = '100%';
  iframe.height = '100%';
  iframe.style.border = 'none';
  iframe.allow = 'autoplay; fullscreen; picture-in-picture';
  iframe.allowFullscreen = true;
  iframe.title = 'Vimeo video player';
  iframe.className = 'video-iframe';

  return iframe;
}

function createVideoElement(url) {
  const video = document.createElement('video');
  video.src = url;
  video.controls = true;
  video.autoplay = true;
  video.className = 'video-element';
  video.style.width = '100%';
  video.style.height = 'auto';
  video.style.maxHeight = '100%';

  return video;
}

function extractYouTubeVideoId(url) {
  const regExp = /^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#&?]*).*/;
  const match = url.match(regExp);
  return (match && match[7].length === 11) ? match[7] : null;
}

function extractVimeoVideoId(url) {
  const regExp = /(?:vimeo)\.com.*(?:videos|video|channels|)\/([\d]+)/i;
  const match = url.match(regExp);
  return match ? match[1] : null;
}

function pauseAndClearVideoContent(popup) {
  // Pause videos before clearing
  const videos = popup.querySelectorAll('video');
  videos.forEach(video => {
    if (!video.paused) {
      video.pause();
    }
  });

  // Remove video elements and class
  const videoElements = popup.querySelectorAll('.video-iframe, .video-element');
  videoElements.forEach(element => element.remove());

  // Remove video class from popup
  popup.classList.remove('popup--video');
}
