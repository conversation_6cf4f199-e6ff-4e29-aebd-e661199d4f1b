// Color Variables
$light-blue: #3e68ff;
$blue: #2f4ebf;
$mid-blue: #1f337e;
$darker-blue: #192965;
$darker-blue-2: #213685;
$dark-blue: #0d1433;
$grey: #f1f1f1;
$grey-light: #f7f7f7;
$grey-dark: #eaeaea;
$green: #34A853;
$red: #DB4437;

// Text Color Variables
$text-color: #000;
$inverted-text-color: #fff;
$footer-text-color: #787c8c;

// Typography Variables
$font-heading: 'Gotham', sans-serif;
$font-body: 'Open Sans', sans-serif;
$font-small: 'Roboto', sans-serif;

$base-font-size: 16px;
$small-font-size: 14px;

// Spacing Variables
$spacing-unit: 8px;
$container-padding: 16px;

// Breakpoints
$breakpoint-xs-max: 575px;
$breakpoint-xs-min: $breakpoint-xs-max + 1;
$breakpoint-sm-max: 767px;
$breakpoint-sm-min: $breakpoint-sm-max + 1;
$breakpoint-md-max: 1023px;
$breakpoint-md-min: $breakpoint-md-max + 1;
$breakpoint-lg-max: 1199px;
$breakpoint-lg-min: $breakpoint-lg-max + 1;

// Height-based breakpoints for hero section
$breakpoint-h-start-max: 1049px;
$breakpoint-h-start-min: $breakpoint-h-start-max + 1;
$breakpoint-h-end-max: 1151px;
$breakpoint-h-end-min: $breakpoint-h-end-max + 1;
$breakpoint-h-limit: 1400px;