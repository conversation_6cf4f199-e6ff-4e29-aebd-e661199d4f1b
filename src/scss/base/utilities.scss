@use "../abstracts/variables" as variables;

// Utilities
.content-desktop {
    display: block;

    @media screen and (max-width: variables.$breakpoint-sm-max) {
        display: none;
    }
}

.content-mobile {
    display: none;

    @media screen and (max-width: variables.$breakpoint-sm-max) {
        display: block;
    }
}

.flex {
    display: flex;
}

.flex-column {
    flex-direction: column;
}

.flex-row {
    flex-direction: row;
}

.flex-wrap {
    flex-wrap: wrap;
}

.justify-center {
    justify-content: center;
}

.justify-end {
    justify-content: flex-end;
}

.justify-stretch {
    justify-content: stretch;
}

.justify-between {
    justify-content: space-between;
}

.align-center {
    align-items: center;
}

.align-stretch {
    align-items: stretch;
}

.h-100 {
    height: 100%;
}