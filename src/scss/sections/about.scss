@use '../abstracts/variables' as variables;

.about {
    padding-top: clamp(25px, calc(12.5px + 6vw), 50px);
    padding-bottom: 75px;

    .row--buttons {
        padding-top: 60px;
    }

    .content-mobile {
        @media screen and (max-width: variables.$breakpoint-sm-max) {
            margin-left: -14px;
            margin-right: -14px;
            width: calc(100% + 28px);
            overflow: hidden;

            img, svg {
                display: block;
                margin: 0 auto;
            }
        }
    }
}

.about__text {
    margin-top: 10px;
    margin-bottom: 30px;
}

.about__tags {
    gap: 20px;

    .tag {
        width: 284px;
        max-width: 100%;
        text-align: center;
        font-family: variables.$font-heading;
        padding: 20px; 
        border-radius: 10px;
        background-color: variables.$grey;
        color: variables.$light-blue;
        font-size: 20px;
        line-height: 1;
    }
}

.about__content {
    margin-top: 80px;
    margin-bottom: 40px;
    width: 1130px;
    max-width: 100%;
    margin-left: auto;
    margin-right: auto;

    h2 {
        margin-bottom: 20px;
    }

    p {
        font-family: variables.$font-heading;
        font-style: normal;
        font-weight: 325;
        font-size: 20px;
        line-height: 32px;
    }
}

.about__items {
    gap: 20px;

    @media screen and (max-width: variables.$breakpoint-sm-max) {
        gap: 35px;
    }

    .col {
        padding-top: 20px;
        flex-basis: calc(100% / 4 - 60px / 4);
        max-width: calc(100% / 4 - 60px / 4);
        display: flex;
        flex-direction: column;
        align-items: center;

        @media screen and (max-width: variables.$breakpoint-md-max) {
            flex-basis: calc(100% / 2 - 20px / 2);
            max-width: calc(100% / 2 - 20px / 2);
        }

        @media screen and (max-width: variables.$breakpoint-sm-max) {
            padding-top: 0;
            flex-basis: 100%;
            max-width: 100%;
        }

        h1, h2, h3, h4, h5, h6 {
            color: variables.$light-blue;
            margin-bottom: 10px;

            @media screen and (max-width: variables.$breakpoint-sm-max) { 
                margin-bottom: 20px;
            }
        }

        > * {
            padding: 0 20px;
        }

        img {
            padding-left: 0;
            padding-right: 0;
            border-radius: 10px;
        }

        p:has(img) {
            padding-left: 0;
            padding-right: 0;
        }

        > p:last-child {
            margin-top: auto;
        }

        > p:has(img):last-child {
            padding-top: 15px;

            @media screen and (max-width: variables.$breakpoint-sm-max) { 
                padding-top: 10px;
            }
        }
    }
}