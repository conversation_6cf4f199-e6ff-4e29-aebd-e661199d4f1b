@use '../abstracts/variables' as variables;

.app {
    background: linear-gradient(180deg, variables.$dark-blue 0%, variables.$light-blue 55.73%, variables.$dark-blue 100%);
    color: variables.$inverted-text-color;
    padding-top: clamp(70px, calc(35px + 6vw), 90px);
    padding-bottom: clamp(70px, calc(35px + 6vw), 90px);

    .row--buttons {
        padding-top: 60px;
    }
}

.app__row {
    width: 1040px;
    max-width: 100%;
    margin: 0 auto;
    gap: 20px;

    @media screen and (max-width: variables.$breakpoint-sm-max) {
        flex-direction: column;
        width: 100%;
        gap: 50px;
        align-items: center;
    }
}

.app__slider {
    width: 210px;
    max-width: 100%;
    flex-shrink: 0;
    position: relative;
    right: -40px;

    @media screen and (max-width: variables.$breakpoint-lg-max) {
        right: 0;
    }
}

.swiper-container {
    overflow: hidden;
}

.app__features {
    margin-top: 40px;
    gap: 3px 25px;

    .col {
        display: flex;
        align-items: center;
    }
}

.app__feature {
    font-family: variables.$font-heading;
    font-style: normal;
    font-weight: 325;
    font-size: 20px;
    line-height: 32px;
    text-align: center;
}

.app__icon {
    i {
        line-height: 140%;
        margin-right: 8px;
        font-size: 24px;
    }
}

.app__text {
    margin-top: 40px;
    background-color: variables.$dark-blue;
    color: variables.$inverted-text-color;
    border-radius: 10px;
    padding: 25px 20px 30px;

    h1, h2, h3, h4, h5, h6 {
        font-size: 26px;
        line-height: 140%;
        margin-bottom: 20px;
    }

    p {
        font-family: variables.$font-heading;
        font-weight: 325;
        line-height: 140%;

        &:last-child {
            margin-bottom: 0;
        }
    }
}

.app__buttons {
    margin-top: 20px;
    gap: 20px;
}