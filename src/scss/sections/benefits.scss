@use '../abstracts/variables' as variables;

.benefits {
    padding-top: clamp(60px, calc(30px + 6vw), 80px);
    padding-bottom: clamp(60px, calc(30px + 6vw), 100px);
    background-color: variables.$grey;

    .row--buttons {
        padding-top: 60px;
    }
}

.benefits__items {
    margin-top: clamp(40px, calc(30px + 6vw), 65px);
    gap: 20px;
    justify-content: center;

    @media screen and (max-width: variables.$breakpoint-sm-max) {
        gap: 30px;
    }

    .col {
        flex-basis: calc(100% / 5 - 80px / 5);
        max-width: calc(100% / 5 - 80px / 5);
        display: flex;
        flex-direction: column;
        align-items: center;

        @media screen and (max-width: variables.$breakpoint-md-max) {
            flex-basis: 240px;
            max-width: 100%;
        }

        i {
            padding: 13px 30px;
            font-size: 64px;
            color: variables.$light-blue;
        }

        h3 {
            margin-top: 15px;
            margin-bottom: 15px;
        }
    }
}

.benefits__text {
    width: 1040px;
    max-width: 100%;
    margin: clamp(30px, calc(20px + 6vw), 65px) auto 0;
    
    p {
        text-align: left;
    
        @media screen and (max-width: variables.$breakpoint-md-max) {
            text-align: center;
        }
    }
}

.benefits__accordion {
    margin-top: 35px;
}
