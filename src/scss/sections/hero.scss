@use '../abstracts/variables' as variables;

.hero {
    align-items: center;
    min-height: calc(100vh - 54px);
    padding-top: clamp(30px, calc(15px + 6vw), 67px);
    padding-bottom: clamp(50px, calc(25px + 6vw), 100px);
    background: linear-gradient(180deg, variables.$dark-blue 23.5%, variables.$darker-blue 44.5%, variables.$mid-blue 55%, variables.$light-blue 72%, variables.$darker-blue-2 86%, variables.$dark-blue 100%);
    color: variables.$inverted-text-color;
    
    @media screen and (min-height: variables.$breakpoint-h-start-min) {
        min-height: calc(100vh - 54px);
        align-items: stretch;
    }

    @media screen and (min-height: variables.$breakpoint-h-end-min) {
        align-items: center;
    }

    @media screen and (min-height: variables.$breakpoint-h-limit) {
        min-height: calc(variables.$breakpoint-h-limit - 54px);
    }

    @media screen and (max-width: variables.$breakpoint-md-max) {
        background: linear-gradient(180deg, variables.$dark-blue 23.5%, variables.$darker-blue 44.5%, variables.$mid-blue 55%, variables.$light-blue 75%, variables.$darker-blue-2 90%, variables.$dark-blue 100%);
    }

    @media screen and (max-width: variables.$breakpoint-sm-max) {
        background: linear-gradient(180deg, variables.$dark-blue 23.5%, variables.$darker-blue 44.5%, variables.$mid-blue 55%, variables.$light-blue 68.27%, variables.$darker-blue-2 75.14%, variables.$dark-blue 100%);
    }

    h1 {
        margin-bottom: clamp(30px, calc(15px + 6vw),  43px);
    }

    .row--buttons {
        padding-top: clamp(50px, calc(25px + 6vw), 70px);
        flex-shrink: 0;
    }
}

.hero__content {
    margin-bottom: 30px;
    flex-shrink: 0;

    p {
        margin-bottom: 0;
    }

    @media screen and (max-width: variables.$breakpoint-md-max) {
        br {
            display: none;
        }
    }
}

.hero__items {
    margin-top: auto;
    margin-bottom: auto;
    gap: 20px;
    align-items: stretch;

    @media screen and (max-width: variables.$breakpoint-md-max) {
        justify-content: center;
    }

    @media screen and (min-width: variables.$breakpoint-sm-min) and (min-height: variables.$breakpoint-h-start-min) {
        flex-grow: 1;
        flex-shrink: 1;
        flex-basis: 0px;
        flex-direction: column;
        min-height: 0;
        margin-top: 0;
        margin-bottom: 0;
    }

    @media screen and (min-height: variables.$breakpoint-h-end-min) {
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;
        min-height: auto;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-top: 0;
        margin-bottom: 0;
        flex-wrap: nowrap;
    }

    .col {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
}

.hero__items__img {
    flex: 0 0 calc(31.428%);
    max-width: calc(31.428%);
    align-self: stretch;
    flex-grow: 1;

    @media screen and (min-width: variables.$breakpoint-md-min) {
        margin-right: -20px;
    }
    
    @media screen and (max-width: variables.$breakpoint-md-max) {
        margin-right: 0;
        flex: 0 0 100%;
        max-width: 100%;
    }

    @media screen and (min-width: variables.$breakpoint-sm-min) and (min-height: variables.$breakpoint-h-start-min) {
        flex-grow: 1;
        flex-shrink: 1;
        flex-basis: 0px;
        min-height: 0;
        max-width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0;
        align-self: auto;
    }
    
    @media screen and (min-height: variables.$breakpoint-h-end-min) {
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;
        min-height: auto;
    }

    img {
        display: block;
        width: 580px;
        height: auto;

        @media screen and (min-height: variables.$breakpoint-h-start-min) {
            width: auto;
            height: 100%;
            max-width: 580px;
            max-height: 100%;
            object-fit: contain;
        }

        @media screen and (min-height: variables.$breakpoint-h-end-min) {
            height: auto;
            width: auto;
            max-width: 580px;
            max-height: 364px;
        }

        @media screen and (max-width: variables.$breakpoint-md-max) {
            max-height: 364px;
            max-width: 100%;
        }

        @media screen and (max-width: variables.$breakpoint-sm-max) and (min-height: variables.$breakpoint-h-start-min) {
            width: 380px;
        }

        @media screen and (max-width: variables.$breakpoint-md-max) and (max-height: variables.$breakpoint-h-start-max) {
            width: 380px;
        }
    }

    p:last-child {
        margin-bottom: 0;
    }
}

.hero__items__icons {
    flex: 0 0 calc(68.572%);
    max-width: calc(68.572%);
    padding: 0;
    gap: 20px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-content: flex-start;

    @media screen and (max-width: variables.$breakpoint-md-max) {
        flex: 0 0 100%;
        max-width: 100%;
    }

    @media screen and (min-height: variables.$breakpoint-h-start-min) {
        margin-left: 0;
        flex-grow: 0;
        flex-shrink: 0;
        flex-basis: auto;
        max-width: 100%;
        width: 100%;
    }
}

.hero__items__icon {
    flex: 0 0 calc(100% / 3 - 40px / 3);
    max-width: calc(100% / 3 - 40px / 3);

    @media screen and (min-width: variables.$breakpoint-md-min) {
        padding-top: 14.5px;
    }

    @media screen and (max-width: variables.$breakpoint-md-max) {
        flex: 0 0 320px;
        max-width: calc(100% / 3 - 40px / 3);
    }

    @media screen and (min-height: variables.$breakpoint-h-start-min) {
        flex: 0 0 320px; 
        max-width: calc(100% / 3 - 40px / 3);
    }

    @media screen and (max-width: variables.$breakpoint-sm-max) {
        flex: 0 0 100%;
        max-width: 100%;
    }

    img, svg {
        width: auto;
        height: 117px;
        display: block;
        margin: 0 auto;
    }
    
    &:nth-child(2) {
        img, svg {
            margin-top: 6.5px;
            margin-bottom: 6.5px;
            height: 104px;
        }
    }

    h2 {
        margin-top: 13px;
        margin-bottom: 13px;
    }

    p {
        width: 250px;
        max-width: 100%;

        &:last-child {
            margin-bottom: 5px;
        }
    }
}