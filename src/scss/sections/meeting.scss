@use '../abstracts/variables' as variables;

.meeting {
    background: linear-gradient(180deg, variables.$dark-blue 0%, variables.$light-blue 55.73%, variables.$dark-blue 100%);
    color: variables.$inverted-text-color;
    padding-top: clamp(60px, calc(30px + 6vw), 70px);
    padding-bottom: clamp(50px, calc(25px + 6vw), 70px);

    .row--buttons {
        padding-top: 60px;
    }
}

.meeting__row {
    gap: 10px;

    @media screen and (max-width: variables.$breakpoint-sm-max) {
        flex-direction: column;
        align-items: center;
        gap: 20px;
    }
}

.meeting__content {
    width: 380px;
    max-width: 100%;

    h2 {
        margin-bottom: clamp(30px, calc(15px + 6vw), 50px);
    }

}

.meeting__img {
    p:last-child {
        margin-bottom: 0;
    }

    @media screen and (max-width: variables.$breakpoint-sm-max) {
        margin-left: -10px;
        margin-right: -10px;
        width: calc(100% + 20px);
        overflow: hidden;

        img, svg {
            display: block;
            margin: 0 auto;
        }
    }
}

.meeting__buttons {
    margin-top: 20px;
    margin-bottom: 10px;
}